{"main_kline_capture_coor": {"p1": [32, 116], "p2": [1171, 571]}, "line_x": [34, 110, 186, 261, 335, 410, 486, 560, 634, 710, 786, 861, 935, 1011, 1085], "line_width": 32, "lines_range": [[2, 66], [78, 142], [154, 218], [229, 293], [303, 367], [378, 442], [454, 518], [528, 592], [602, 666], [678, 742], [754, 818], [829, 893], [903, 967], [979, 1043], [1053, 1117]], "window": {"windows_size": [1920, 1080], "search_box_coor": [1920, 1980]}, "sql_config_info": {"host": "**************", "user": "ken", "password": "li173312", "database": "ths_stock", "charset": "utf8mb4", "cursorclass": "pymysql.cursors.Cursor"}, "debug_config": {"enable_debug_mode": true, "save_intermediate_images": true, "debug_output_dir": "resources/cache/debug", "save_original_screenshot": true, "save_cropped_image": true, "save_detection_result": true, "image_format": "jpg", "add_timestamp_to_filename": true}}