{"main_kline_capture_coor": {"p1": [43, 319], "p2": [119, 319]}, "line_x": [49, 122, 198, 272, 346, 424, 497, 573, 647, 723, 798, 872, 947, 1022, 1096], "line_width": 31, "lines_range": [[18, 80], [91, 153], [167, 229], [241, 303], [315, 377], [393, 455], [466, 528], [542, 604], [616, 678], [692, 754], [767, 829], [841, 903], [916, 978], [991, 1053], [1065, 1127]], "window": {"windows_size": [1920, 1080], "search_box_coor": [1920, 1980]}, "sql_config_info": {"host": "**************", "user": "ken", "password": "li173312", "database": "ths_stock", "charset": "utf8mb4", "cursorclass": "pymysql.cursors.Cursor"}, "debug_config": {"enable_debug_mode": true, "save_intermediate_images": true, "debug_output_dir": "resources/cache/debug", "save_original_screenshot": true, "save_cropped_image": true, "save_detection_result": true, "image_format": "jpg", "add_timestamp_to_filename": true}, "interactive_capture": {"last_screenshot_path": "resources\\images\\interactive_screenshot_20250823_234647.png", "original_selection": {"p1": [95, 122], "p2": [1234, 545]}, "kline_selection": {"p1": [43, 319], "p2": [119, 319]}, "timestamp": "2025-08-23 23:47:16", "kline_centers": [49, 122, 198, 272, 346, 424, 497, 573, 647, 723, 798, 872, 947, 1022, 1096], "kline_count": 15}}