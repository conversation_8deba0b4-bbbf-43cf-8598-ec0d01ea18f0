import get_screenshot_tool
import lines_detetor
import capture_imge
import json
"""
使用这个文件对窗口进行预处理
1. 使用影刀进行截图，保存到"C:/Users/<USER>/Documents/ths_gs_resource/template/precessing_main_widnows.jpg"
2. 调用screenshot_tool来手动指定K线的范围，将p1和p2保存到json文件
3. 调用capture_image中的resize_img_fullimg来截图, 注意截图的时候需要正好截到两边K线停止的地方
4. 调用lines_detetor来识别K线的坐标，将15根K线的坐标保存到json文件
"""
def main():
    json_path = r"resources/coor_config.json"
    with open(json_path, 'r', encoding='utf-8') as json_file:
        # 2. 使用 json.load() 解析文件内容
        data = json.load(json_file)
    # 第一步，假设已经做完

    # 第二步
    image_path = r"C:\Users\<USER>\Documents\ths_autowin\resources\cache\img\60min\513050_screenshot_20250823_210541.png"
    coor_list = get_screenshot_tool.get_coor(image_path)
    data["main_kline_capture_coor"]["p1"]= coor_list[0]
    data["main_kline_capture_coor"]["p2"]= coor_list[1]
    #第三步
    resize_image_path = capture_imge.resize_img_fullimg(image_path, coor_list)

    #第四步
    # resize_image_path = r"C:\Users\<USER>\Documents\ths_gs_resource\template\cropped_kline_precessing_main_widnows.jpg"
    coors = lines_detetor.detect_and_predict_vertical_lines(resize_image_path,mode='manual')
    data["line_x"] = coors
    data["line_width"] = int(0.85*(coors[1]-coors[0])/2)
    lines_range = []
    for i in range(len(coors)):
        lines_range.append([coors[i]-data["line_width"], coors[i]+data["line_width"]])
    data["lines_range"] = lines_range
    with open(json_path, 'w', encoding='utf-8') as json_file:
        json.dump(data, json_file, ensure_ascii=False, indent=4)

if __name__ == '__main__':
    main()    


